<template>
  <swiper
    :spaceBetween="30"
    :centeredSlides="true"
    :loop="true"
    :autoplay="{
      delay: 2500,
      disableOnInteraction: false,
    }"
    :pagination="{
      clickable: true,
    }"
    :navigation="true"
    :modules="modules"
    class="mySwiper"
  >
    <swiper-slide><img src="../assets/img/slide1.jpg" alt="Slide 1"></swiper-slide>
    <swiper-slide><img src="../assets/img/slide2.jpg" alt="Slide 2"></swiper-slide>
    <swiper-slide><img src="../assets/img/slide3.jpg" alt="Slide 3"></swiper-slide>
    <swiper-slide><img src="../assets/img/slide4.jpg" alt="Slide 4"></swiper-slide>
    <swiper-slide><img src="../assets/img/slide5.jpg" alt="Slide 5"></swiper-slide>
    
  </swiper>
</template>
<script>
  // Import Swiper Vue.js components
  import { Swiper, SwiperSlide } from 'swiper/vue';

  // Import Swiper styles
  import 'swiper/css';

  import 'swiper/css/pagination';
  import 'swiper/css/navigation';

import './../assets/css/Carousel.css';

  // import required modules
  import { Autoplay, Pagination, Navigation } from 'swiper/modules';

  export default {
    components: {
      Swiper,
      SwiperSlide,
    },
    setup() {
      return {
        modules: [Autoplay, Pagination, Navigation],
      };
    },
  };
</script>
